<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>站内信回复功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-test-tube me-2"></i>站内信回复功能测试</h5>
                    </div>
                    <div class="card-body">
                        <h6>测试步骤：</h6>
                        <ol>
                            <li>访问站内信页面：<a href="/messages" target="_blank">/messages</a></li>
                            <li>查找有回复数量徽章的消息</li>
                            <li>点击"回复"按钮测试回复功能</li>
                            <li>点击"查看"按钮查看消息线程</li>
                        </ol>
                        
                        <h6 class="mt-4">当前测试数据：</h6>
                        <div id="testData">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button class="btn btn-primary" onclick="loadTestData()">
                                <i class="fas fa-refresh me-1"></i>刷新测试数据
                            </button>
                            <a href="/messages" class="btn btn-success ms-2">
                                <i class="fas fa-envelope me-1"></i>访问站内信页面
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function loadTestData() {
            const testDataDiv = document.getElementById('testData');
            testDataDiv.innerHTML = '<div class="spinner-border" role="status"><span class="visually-hidden">加载中...</span></div>';
            
            fetch('/api/messages?limit=10')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = '<div class="table-responsive"><table class="table table-sm">';
                        html += '<thead><tr><th>标题</th><th>发送者</th><th>回复数</th><th>状态</th></tr></thead><tbody>';
                        
                        data.messages.forEach(msg => {
                            const replyCount = msg.replies ? msg.replies.length : 0;
                            const status = msg.is_read ? '<span class="badge bg-success">已读</span>' : '<span class="badge bg-warning">未读</span>';
                            html += `<tr>
                                <td>${msg.title}</td>
                                <td>${msg.sender}</td>
                                <td>${replyCount > 0 ? `<span class="badge bg-info">${replyCount}</span>` : '0'}</td>
                                <td>${status}</td>
                            </tr>`;
                        });
                        
                        html += '</tbody></table></div>';
                        html += `<p class="text-muted mt-2">总消息数: ${data.messages.length}, 未读消息: ${data.unread_count}</p>`;
                        testDataDiv.innerHTML = html;
                    } else {
                        testDataDiv.innerHTML = '<div class="alert alert-danger">加载失败: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    console.error('加载测试数据失败:', error);
                    testDataDiv.innerHTML = '<div class="alert alert-danger">加载失败，请检查网络连接</div>';
                });
        }
        
        // 页面加载时自动加载测试数据
        document.addEventListener('DOMContentLoaded', loadTestData);
    </script>
</body>
</html>
