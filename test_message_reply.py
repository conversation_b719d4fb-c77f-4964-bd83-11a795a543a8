#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试站内信回复功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from message_system import MessageSystem
from auth import UserManager

def test_message_reply_system():
    """测试站内信回复功能"""
    print("=== 站内信回复功能测试 ===")
    
    # 创建测试实例
    message_system = MessageSystem('test_messages.json')
    user_manager = UserManager('test_users.json')
    
    # 创建测试用户
    print("\n1. 创建测试用户...")
    user_manager.register_user('admin_user', '<EMAIL>', 'password123')
    user_manager.register_user('normal_user', '<EMAIL>', 'password123')
    
    # 设置管理员权限
    admin_user = user_manager.get_user('admin_user')
    admin_user['is_admin'] = True
    user_manager.save_users()
    
    print("✓ 测试用户创建完成")
    
    # 测试发送原始消息
    print("\n2. 发送原始消息...")
    success, message_id = message_system.send_message(
        sender='admin_user',
        recipient='normal_user',
        title='关于您的申请',
        content='您好，我们已收到您的申请，需要补充一些材料。',
        message_type='admin'
    )
    
    if success:
        print(f"✓ 原始消息发送成功，消息ID: {message_id}")
        original_message_id = message_id
    else:
        print(f"✗ 原始消息发送失败: {message_id}")
        return
    
    # 测试回复消息
    print("\n3. 用户回复消息...")
    success, reply_id = message_system.send_message(
        sender='normal_user',
        recipient='admin_user',
        title='Re: 关于您的申请',
        content='好的，请问需要补充哪些材料？',
        message_type='user',
        reply_to=original_message_id
    )
    
    if success:
        print(f"✓ 回复消息发送成功，回复ID: {reply_id}")
    else:
        print(f"✗ 回复消息发送失败: {reply_id}")
        return
    
    # 测试管理员再次回复
    print("\n4. 管理员再次回复...")
    success, reply2_id = message_system.send_message(
        sender='admin_user',
        recipient='normal_user',
        title='Re: 关于您的申请',
        content='请提供身份证明和相关证书的扫描件。',
        message_type='admin',
        reply_to=original_message_id
    )
    
    if success:
        print(f"✓ 管理员回复发送成功，回复ID: {reply2_id}")
    else:
        print(f"✗ 管理员回复发送失败: {reply2_id}")
        return
    
    # 测试获取消息线程
    print("\n5. 获取消息线程...")
    thread = message_system.get_message_thread(original_message_id, 'normal_user')
    
    if thread['original']:
        print(f"✓ 获取消息线程成功")
        print(f"  - 原消息: {thread['original']['title']}")
        print(f"  - 回复数量: {len(thread['replies'])}")
        
        for i, reply in enumerate(thread['replies'], 1):
            print(f"  - 回复{i}: {reply['sender']} -> {reply['content'][:30]}...")
    else:
        print("✗ 获取消息线程失败")
        return
    
    # 测试获取用户消息列表
    print("\n6. 获取用户消息列表...")
    user_messages = message_system.get_user_messages('normal_user', limit=10)
    print(f"✓ 用户消息数量: {len(user_messages)}")
    
    for msg in user_messages:
        reply_count = len(msg.get('replies', []))
        print(f"  - {msg['title']} (回复: {reply_count})")
    
    # 测试未读消息统计
    print("\n7. 测试未读消息统计...")
    unread_count = message_system.get_unread_count('normal_user')
    print(f"✓ 用户未读消息数量: {unread_count}")
    
    # 测试标记消息为已读
    print("\n8. 标记消息为已读...")
    success, msg = message_system.mark_as_read(original_message_id, 'normal_user')
    if success:
        print(f"✓ 标记已读成功: {msg}")
    else:
        print(f"✗ 标记已读失败: {msg}")
    
    # 再次检查未读数量
    unread_count_after = message_system.get_unread_count('normal_user')
    print(f"✓ 标记后未读消息数量: {unread_count_after}")
    
    print("\n=== 测试完成 ===")
    
    # 清理测试文件
    cleanup_files = ['test_messages.json', 'test_users.json']
    for file in cleanup_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"✓ 清理测试文件: {file}")

if __name__ == "__main__":
    test_message_reply_system()
