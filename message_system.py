#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
站内信系统
用于管理用户之间的站内消息
"""

import json
import os
import uuid
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from beijing_time import beijing_now_iso


class MessageSystem:
    """站内信系统"""

    def __init__(self, data_file: str = 'messages.json'):
        self.data_file = data_file
        self.messages_data = self.load_messages_data()
        # 执行数据迁移（如果需要）
        self._migrate_data_if_needed()
    
    def load_messages_data(self) -> Dict:
        """加载消息数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"加载消息数据失败: {e}")
                return self.create_default_data()
        else:
            return self.create_default_data()
    
    def create_default_data(self) -> Dict:
        """创建默认数据结构"""
        return {
            'messages': [],  # 消息记录
            'statistics': {
                'total_messages': 0,
                'total_read_messages': 0,
                'total_unread_messages': 0,
                'total_system_messages': 0,
                'total_admin_messages': 0
            }
        }
    
    def save_messages_data(self) -> bool:
        """保存消息数据"""
        try:
            # 创建备份
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup"
                with open(self.data_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            
            # 保存新数据
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.messages_data, f, ensure_ascii=False, indent=2)
            return True
        except IOError as e:
            print(f"保存消息数据失败: {e}")
            return False
    
    def send_message(self, sender: str, recipient: str, title: str, content: str,
                    message_type: str = 'user', reply_to: str = None) -> Tuple[bool, str]:
        """发送站内信

        Args:
            sender: 发送者用户名
            recipient: 接收者用户名 ('all' 表示发送给所有用户)
            title: 消息标题
            content: 消息内容
            message_type: 消息类型 ('user', 'admin', 'system')
            reply_to: 回复的消息ID（可选）

        Returns:
            (success, message_id or error_message)
        """
        try:
            if not title.strip() or not content.strip():
                return False, "标题和内容不能为空"

            message_id = str(uuid.uuid4())
            timestamp = beijing_now_iso()

            message = {
                'id': message_id,
                'sender': sender,
                'recipient': recipient,
                'title': title.strip(),
                'content': content.strip(),
                'type': message_type,
                'timestamp': timestamp,
                'read_by': {},  # 新格式：记录每个用户的已读状态
                'reply_to': reply_to,  # 回复的消息ID
                'replies': []  # 回复消息ID列表
            }

            # 如果是回复消息，更新原消息的回复列表
            if reply_to:
                original_message = self.get_message_by_id(reply_to)
                if original_message:
                    # 找到原消息并添加回复ID
                    for msg in self.messages_data['messages']:
                        if msg['id'] == reply_to:
                            if 'replies' not in msg:
                                msg['replies'] = []
                            msg['replies'].append(message_id)
                            break

            self.messages_data['messages'].append(message)

            # 更新统计信息
            self.messages_data['statistics']['total_messages'] += 1
            self.messages_data['statistics']['total_unread_messages'] += 1

            if message_type == 'system':
                self.messages_data['statistics']['total_system_messages'] += 1
            elif message_type == 'admin':
                self.messages_data['statistics']['total_admin_messages'] += 1

            if self.save_messages_data():
                return True, message_id
            else:
                return False, "保存消息失败"

        except Exception as e:
            print(f"发送消息失败: {e}")
            return False, f"发送失败: {str(e)}"
    
    def get_user_messages(self, username: str, limit: int = 50,
                         offset: int = 0, unread_only: bool = False) -> List[Dict]:
        """获取用户的消息列表

        Args:
            username: 用户名
            limit: 返回消息数量限制
            offset: 偏移量
            unread_only: 是否只返回未读消息

        Returns:
            消息列表（包含用户特定的已读状态）
        """
        try:
            # 筛选属于该用户的消息（包括发给所有人的消息）
            user_messages = []
            for message in self.messages_data['messages']:
                if message['recipient'] == username or message['recipient'] == 'all':
                    # 检查是否只返回未读消息
                    is_read_by_user = self._is_message_read_by_user(message, username)
                    if not unread_only or not is_read_by_user:
                        # 创建消息副本并添加用户特定的已读信息
                        user_message = message.copy()
                        user_message['is_read'] = is_read_by_user
                        user_message['read_at'] = self._get_message_read_time_by_user(message, username)
                        user_messages.append(user_message)

            # 按时间倒序排列
            user_messages.sort(key=lambda x: x['timestamp'], reverse=True)

            # 应用分页
            return user_messages[offset:offset + limit]

        except Exception as e:
            print(f"获取用户消息失败: {e}")
            return []
    
    def get_unread_count(self, username: str) -> int:
        """获取用户未读消息数量"""
        try:
            count = 0
            for message in self.messages_data['messages']:
                if (message['recipient'] == username or message['recipient'] == 'all'):
                    if not self._is_message_read_by_user(message, username):
                        count += 1
            return count
        except Exception as e:
            print(f"获取未读消息数量失败: {e}")
            return 0
    
    def mark_as_read(self, message_id: str, username: str) -> Tuple[bool, str]:
        """标记消息为已读

        Args:
            message_id: 消息ID
            username: 用户名

        Returns:
            (success, message)
        """
        try:
            for message in self.messages_data['messages']:
                if (message['id'] == message_id and
                    (message['recipient'] == username or message['recipient'] == 'all')):

                    # 确保read_by字段存在
                    if 'read_by' not in message:
                        message['read_by'] = {}

                    # 检查用户是否已经标记为已读
                    if username not in message['read_by']:
                        # 标记该用户为已读
                        message['read_by'][username] = beijing_now_iso()

                        # 注意：不再更新全局统计信息，因为统计信息现在基于是否有任何用户已读
                        # 统计信息将在需要时重新计算

                        if self.save_messages_data():
                            return True, "消息已标记为已读"
                        else:
                            return False, "保存失败"
                    else:
                        return True, "消息已经是已读状态"

            return False, "消息不存在或无权限"

        except Exception as e:
            print(f"标记消息已读失败: {e}")
            return False, f"操作失败: {str(e)}"
    
    def mark_all_as_read(self, username: str) -> Tuple[bool, str]:
        """标记用户所有消息为已读"""
        try:
            marked_count = 0
            timestamp = beijing_now_iso()

            for message in self.messages_data['messages']:
                if (message['recipient'] == username or message['recipient'] == 'all'):
                    # 确保read_by字段存在
                    if 'read_by' not in message:
                        message['read_by'] = {}

                    # 检查用户是否未读此消息
                    if username not in message['read_by']:
                        message['read_by'][username] = timestamp
                        marked_count += 1

            if marked_count > 0:
                if self.save_messages_data():
                    return True, f"已标记 {marked_count} 条消息为已读"
                else:
                    return False, "保存失败"
            else:
                return True, "没有未读消息"

        except Exception as e:
            print(f"批量标记已读失败: {e}")
            return False, f"操作失败: {str(e)}"
    
    def delete_message(self, message_id: str, username: str) -> Tuple[bool, str]:
        """删除消息（仅限用户删除自己收到的消息）

        注意：对于发给所有人的消息，这里实际上是从用户的视角"删除"消息，
        但消息本身仍然存在，其他用户仍然可以看到。
        如果需要真正删除消息，应该由管理员操作。
        """
        try:
            for i, message in enumerate(self.messages_data['messages']):
                if (message['id'] == message_id and
                    (message['recipient'] == username or message['recipient'] == 'all')):

                    # 对于个人消息，直接删除
                    if message['recipient'] == username:
                        # 更新统计信息
                        self.messages_data['statistics']['total_messages'] -= 1
                        # 删除消息
                        del self.messages_data['messages'][i]

                        if self.save_messages_data():
                            return True, "消息已删除"
                        else:
                            return False, "保存失败"

                    # 对于群发消息，我们不能真正删除，只能标记用户已"删除"
                    # 这里可以扩展一个deleted_by字段来记录哪些用户删除了消息
                    else:
                        # 暂时返回不支持删除群发消息
                        return False, "无法删除群发消息，请联系管理员"

            return False, "消息不存在或无权限"

        except Exception as e:
            print(f"删除消息失败: {e}")
            return False, f"删除失败: {str(e)}"
    
    def get_message_by_id(self, message_id: str) -> Optional[Dict]:
        """根据ID获取消息"""
        try:
            for message in self.messages_data['messages']:
                if message['id'] == message_id:
                    return message.copy()
            return None
        except Exception as e:
            print(f"获取消息失败: {e}")
            return None

    def get_message_replies(self, message_id: str) -> List[Dict]:
        """获取消息的所有回复"""
        try:
            replies = []
            for message in self.messages_data['messages']:
                if message.get('reply_to') == message_id:
                    replies.append(message.copy())

            # 按时间排序
            replies.sort(key=lambda x: x['timestamp'])
            return replies
        except Exception as e:
            print(f"获取消息回复失败: {e}")
            return []

    def get_message_thread(self, message_id: str, username: str) -> Dict:
        """获取消息线程（原消息+所有回复）"""
        try:
            # 获取原消息
            original_message = self.get_message_by_id(message_id)
            if not original_message:
                return {'original': None, 'replies': []}

            # 检查权限
            if (original_message['recipient'] != username and
                original_message['recipient'] != 'all' and
                original_message['sender'] != username):
                return {'original': None, 'replies': []}

            # 添加用户特定的已读状态
            original_message['is_read'] = self._is_message_read_by_user(original_message, username)
            original_message['read_at'] = self._get_message_read_time_by_user(original_message, username)

            # 获取回复
            replies = []
            for message in self.messages_data['messages']:
                if message.get('reply_to') == message_id:
                    # 检查回复权限
                    if (message['recipient'] == username or
                        message['recipient'] == 'all' or
                        message['sender'] == username):
                        reply = message.copy()
                        reply['is_read'] = self._is_message_read_by_user(reply, username)
                        reply['read_at'] = self._get_message_read_time_by_user(reply, username)
                        replies.append(reply)

            # 按时间排序
            replies.sort(key=lambda x: x['timestamp'])

            return {
                'original': original_message,
                'replies': replies
            }
        except Exception as e:
            print(f"获取消息线程失败: {e}")
            return {'original': None, 'replies': []}
    
    def get_statistics(self) -> Dict:
        """获取消息统计信息"""
        return self.messages_data['statistics'].copy()
    
    def cleanup_old_messages(self, days_to_keep: int = 30) -> Tuple[bool, str]:
        """清理旧消息"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cutoff_iso = cutoff_date.isoformat()
            
            original_count = len(self.messages_data['messages'])
            
            # 保留较新的消息
            self.messages_data['messages'] = [
                msg for msg in self.messages_data['messages']
                if msg['timestamp'] > cutoff_iso
            ]
            
            deleted_count = original_count - len(self.messages_data['messages'])
            
            if deleted_count > 0:
                # 重新计算统计信息
                self.recalculate_statistics()
                
                if self.save_messages_data():
                    return True, f"已清理 {deleted_count} 条旧消息"
                else:
                    return False, "保存失败"
            else:
                return True, "没有需要清理的消息"
                
        except Exception as e:
            print(f"清理旧消息失败: {e}")
            return False, f"清理失败: {str(e)}"
    
    def recalculate_statistics(self):
        """重新计算统计信息"""
        stats = {
            'total_messages': len(self.messages_data['messages']),
            'total_read_messages': 0,
            'total_unread_messages': 0,
            'total_system_messages': 0,
            'total_admin_messages': 0
        }

        for message in self.messages_data['messages']:
            # 检查是否有任何用户已读（用于统计）
            has_any_read = False
            if 'read_by' in message and message['read_by']:
                has_any_read = True
            elif message.get('is_read', False):  # 向后兼容
                has_any_read = True

            if has_any_read:
                stats['total_read_messages'] += 1
            else:
                stats['total_unread_messages'] += 1

            if message['type'] == 'system':
                stats['total_system_messages'] += 1
            elif message['type'] == 'admin':
                stats['total_admin_messages'] += 1

        self.messages_data['statistics'] = stats

    def _migrate_data_if_needed(self):
        """数据迁移：将旧的单一已读状态转换为多用户已读状态，并添加回复功能字段"""
        try:
            migrated = False
            for message in self.messages_data['messages']:
                # 检查是否需要迁移（存在旧格式的is_read字段但没有read_by字段）
                if 'is_read' in message and 'read_by' not in message:
                    # 初始化新的read_by字段
                    message['read_by'] = {}

                    # 如果旧数据显示已读，我们无法知道是哪个用户读的
                    # 所以只能清空已读状态，让用户重新标记
                    if message.get('is_read', False):
                        print(f"警告: 消息 {message['id']} 的已读状态已重置，需要用户重新标记")

                    # 移除旧字段
                    message.pop('is_read', None)
                    message.pop('read_at', None)
                    migrated = True

                # 确保所有消息都有read_by字段
                elif 'read_by' not in message:
                    message['read_by'] = {}
                    migrated = True

                # 添加回复功能相关字段
                if 'reply_to' not in message:
                    message['reply_to'] = None
                    migrated = True

                if 'replies' not in message:
                    message['replies'] = []
                    migrated = True

            if migrated:
                print("数据迁移完成：已将单一已读状态转换为多用户已读状态，并添加回复功能支持")
                # 重新计算统计信息
                self.recalculate_statistics()
                # 保存迁移后的数据
                if self.save_messages_data():
                    print("迁移数据已保存到文件")
                else:
                    print("警告：迁移数据保存失败")

        except Exception as e:
            print(f"数据迁移失败: {e}")

    def _is_message_read_by_user(self, message: Dict, username: str) -> bool:
        """检查消息是否被指定用户已读"""
        if 'read_by' in message and message['read_by']:
            return username in message['read_by']
        # 向后兼容：检查旧格式
        return message.get('is_read', False)

    def _get_message_read_time_by_user(self, message: Dict, username: str) -> Optional[str]:
        """获取用户读取消息的时间"""
        if 'read_by' in message and message['read_by'] and username in message['read_by']:
            return message['read_by'][username]
        # 向后兼容：返回旧格式的读取时间
        if message.get('is_read', False):
            return message.get('read_at')
        return None
