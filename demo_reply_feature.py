#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示站内信回复功能
为现有消息添加回复以展示功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from message_system import MessageSystem
from auth import UserManager

def demo_reply_feature():
    """演示站内信回复功能"""
    print("=== 站内信回复功能演示 ===")
    
    # 使用实际的数据文件
    message_system = MessageSystem('messages.json')
    user_manager = UserManager('users.json')
    
    print("\n1. 当前消息统计:")
    stats = message_system.get_statistics()
    print(f"   总消息数: {stats['total_messages']}")
    print(f"   未读消息: {stats['total_unread_messages']}")
    print(f"   已读消息: {stats['total_read_messages']}")
    
    # 获取一些现有消息来添加回复
    messages = message_system.messages_data['messages']
    
    # 找到一个管理员发给用户的消息来演示回复
    admin_message = None
    for msg in messages:
        if (msg['sender'] == 'admin' and 
            msg['recipient'] not in ['all', 'admin'] and 
            len(msg.get('replies', [])) == 0):
            admin_message = msg
            break
    
    if admin_message:
        print(f"\n2. 找到管理员消息: {admin_message['title']}")
        print(f"   发送给: {admin_message['recipient']}")
        print(f"   内容: {admin_message['content'][:50]}...")
        
        # 模拟用户回复
        print("\n3. 添加用户回复...")
        success, reply_id = message_system.send_message(
            sender=admin_message['recipient'],
            recipient='admin',
            title=f"Re: {admin_message['title']}",
            content="谢谢管理员的通知！我已经了解了相关信息。请问还有其他需要注意的事项吗？",
            message_type='user',
            reply_to=admin_message['id']
        )
        
        if success:
            print(f"   ✓ 用户回复成功，回复ID: {reply_id}")
            
            # 模拟管理员再次回复
            print("\n4. 添加管理员回复...")
            success2, reply2_id = message_system.send_message(
                sender='admin',
                recipient=admin_message['recipient'],
                title=f"Re: {admin_message['title']}",
                content="感谢您的回复！目前没有其他特别需要注意的事项。如果您在使用过程中遇到任何问题，随时可以通过站内信联系我们。",
                message_type='admin',
                reply_to=admin_message['id']
            )
            
            if success2:
                print(f"   ✓ 管理员回复成功，回复ID: {reply2_id}")
            else:
                print(f"   ✗ 管理员回复失败: {reply2_id}")
        else:
            print(f"   ✗ 用户回复失败: {reply_id}")
    
    # 找到另一个消息来演示多轮对话
    feedback_message = None
    for msg in messages:
        if (msg['title'].find('反馈') != -1 and 
            len(msg.get('replies', [])) == 0):
            feedback_message = msg
            break
    
    if feedback_message:
        print(f"\n5. 找到反馈消息: {feedback_message['title']}")
        
        # 添加多轮对话
        print("\n6. 添加多轮对话演示...")
        
        # 第一轮回复
        success, reply1 = message_system.send_message(
            sender=feedback_message['recipient'],
            recipient=feedback_message['sender'],
            title=f"Re: {feedback_message['title']}",
            content="感谢您的反馈！我们已经收到并正在处理。能否提供更多详细信息？",
            message_type='admin',
            reply_to=feedback_message['id']
        )
        
        if success:
            print("   ✓ 第一轮回复成功")
            
            # 第二轮回复
            success, reply2 = message_system.send_message(
                sender=feedback_message['sender'],
                recipient=feedback_message['recipient'],
                title=f"Re: {feedback_message['title']}",
                content="好的，具体情况是这样的：我在使用过程中发现了一些可以改进的地方...",
                message_type='user',
                reply_to=feedback_message['id']
            )
            
            if success:
                print("   ✓ 第二轮回复成功")
                
                # 第三轮回复
                success, reply3 = message_system.send_message(
                    sender=feedback_message['recipient'],
                    recipient=feedback_message['sender'],
                    title=f"Re: {feedback_message['title']}",
                    content="非常感谢您的详细反馈！我们会认真考虑您的建议，并在下个版本中进行改进。",
                    message_type='admin',
                    reply_to=feedback_message['id']
                )
                
                if success:
                    print("   ✓ 第三轮回复成功")
    
    print("\n7. 演示完成后的统计:")
    stats_after = message_system.get_statistics()
    print(f"   总消息数: {stats_after['total_messages']} (增加了 {stats_after['total_messages'] - stats['total_messages']})")
    print(f"   未读消息: {stats_after['total_unread_messages']}")
    
    # 展示消息线程
    if admin_message:
        print(f"\n8. 展示消息线程 (消息ID: {admin_message['id']}):")
        thread = message_system.get_message_thread(admin_message['id'], admin_message['recipient'])
        
        if thread['original']:
            print(f"   原消息: {thread['original']['title']}")
            print(f"   发送者: {thread['original']['sender']}")
            print(f"   内容: {thread['original']['content'][:50]}...")
            print(f"   回复数量: {len(thread['replies'])}")
            
            for i, reply in enumerate(thread['replies'], 1):
                print(f"   回复{i}: {reply['sender']} -> {reply['content'][:30]}...")
    
    print("\n=== 演示完成 ===")
    print("\n现在您可以在浏览器中访问站内信页面查看回复功能！")
    print("- 登录后访问 /messages 页面")
    print("- 点击消息查看详情，可以看到回复")
    print("- 点击回复按钮可以发送回复")

if __name__ == "__main__":
    demo_reply_feature()
