#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from message_system import MessageSystem

ms = MessageSystem()

# 找到有回复的消息
for msg in ms.messages_data['messages']:
    if len(msg.get('replies', [])) > 0:
        print(f"Testing thread for message: {msg['title']}")
        print(f"Message ID: {msg['id']}")
        print(f"Recipient: {msg['recipient']}")
        
        # 获取消息线程
        thread = ms.get_message_thread(msg['id'], msg['recipient'])
        
        if thread['original']:
            print(f"\nOriginal message: {thread['original']['title']}")
            print(f"Content: {thread['original']['content'][:50]}...")
            print(f"Number of replies: {len(thread['replies'])}")
            
            for i, reply in enumerate(thread['replies'], 1):
                print(f"  Reply {i}: {reply['sender']} -> {reply['content'][:30]}...")
        else:
            print("Failed to get thread")
        
        break
