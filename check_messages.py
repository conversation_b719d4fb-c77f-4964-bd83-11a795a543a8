#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from message_system import MessageSystem

ms = MessageSystem()
print(f"Total messages: {len(ms.messages_data['messages'])}")
print("\nRecent messages:")
for msg in ms.messages_data['messages'][-5:]:
    replies_count = len(msg.get('replies', []))
    print(f"- {msg['title']} (replies: {replies_count})")

# 检查是否有回复
has_replies = False
for msg in ms.messages_data['messages']:
    if len(msg.get('replies', [])) > 0:
        has_replies = True
        print(f"\nMessage with replies: {msg['title']}")
        print(f"  Replies: {msg['replies']}")

if not has_replies:
    print("\nNo messages with replies found.")
