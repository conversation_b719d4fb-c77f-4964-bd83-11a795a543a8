#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证站内信回复功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from message_system import MessageSystem

def final_verification():
    """最终验证所有功能"""
    print("=== 站内信回复功能最终验证 ===")
    
    ms = MessageSystem()
    
    print(f"\n1. 数据统计:")
    stats = ms.get_statistics()
    print(f"   总消息数: {stats['total_messages']}")
    print(f"   未读消息: {stats['total_unread_messages']}")
    print(f"   已读消息: {stats['total_read_messages']}")
    
    print(f"\n2. 数据迁移验证:")
    migration_ok = True
    for msg in ms.messages_data['messages']:
        if 'reply_to' not in msg or 'replies' not in msg:
            migration_ok = False
            break
    print(f"   数据迁移状态: {'✓ 成功' if migration_ok else '✗ 失败'}")
    
    print(f"\n3. 回复功能验证:")
    messages_with_replies = 0
    total_replies = 0
    
    for msg in ms.messages_data['messages']:
        reply_count = len(msg.get('replies', []))
        if reply_count > 0:
            messages_with_replies += 1
            total_replies += reply_count
            print(f"   消息 '{msg['title'][:30]}...' 有 {reply_count} 条回复")
    
    print(f"   有回复的消息数: {messages_with_replies}")
    print(f"   总回复数: {total_replies}")
    
    print(f"\n4. 线程功能验证:")
    if messages_with_replies > 0:
        # 找到第一个有回复的消息进行线程测试
        for msg in ms.messages_data['messages']:
            if len(msg.get('replies', [])) > 0:
                thread = ms.get_message_thread(msg['id'], msg['recipient'])
                if thread['original']:
                    print(f"   ✓ 线程获取成功")
                    print(f"   原消息: {thread['original']['title']}")
                    print(f"   回复数量: {len(thread['replies'])}")
                    for i, reply in enumerate(thread['replies'], 1):
                        print(f"     回复{i}: {reply['sender']} -> {reply['content'][:20]}...")
                else:
                    print(f"   ✗ 线程获取失败")
                break
    else:
        print(f"   无回复消息，跳过线程测试")
    
    print(f"\n5. API接口验证:")
    # 模拟API调用
    try:
        # 测试获取消息列表
        user_messages = ms.get_user_messages('admin', limit=5)
        print(f"   ✓ 获取用户消息: {len(user_messages)} 条")
        
        # 测试未读消息统计
        unread_count = ms.get_unread_count('admin')
        print(f"   ✓ 未读消息统计: {unread_count} 条")
        
        # 测试消息详情获取
        if ms.messages_data['messages']:
            first_msg = ms.messages_data['messages'][0]
            msg_detail = ms.get_message_by_id(first_msg['id'])
            print(f"   ✓ 消息详情获取: {'成功' if msg_detail else '失败'}")
        
    except Exception as e:
        print(f"   ✗ API接口测试失败: {e}")
    
    print(f"\n6. 功能完整性检查:")
    
    # 检查必要的字段
    required_fields = ['id', 'sender', 'recipient', 'title', 'content', 'type', 'timestamp', 'read_by', 'reply_to', 'replies']
    field_check = True
    
    for msg in ms.messages_data['messages'][:3]:  # 检查前3条消息
        for field in required_fields:
            if field not in msg:
                field_check = False
                print(f"   ✗ 消息缺少字段: {field}")
                break
        if not field_check:
            break
    
    if field_check:
        print(f"   ✓ 消息字段完整")
    
    # 检查回复关系的一致性
    reply_consistency = True
    for msg in ms.messages_data['messages']:
        for reply_id in msg.get('replies', []):
            # 查找回复消息
            reply_msg = ms.get_message_by_id(reply_id)
            if not reply_msg:
                print(f"   ✗ 回复消息不存在: {reply_id}")
                reply_consistency = False
            elif reply_msg.get('reply_to') != msg['id']:
                print(f"   ✗ 回复关系不一致: {reply_id}")
                reply_consistency = False
    
    if reply_consistency:
        print(f"   ✓ 回复关系一致")
    
    print(f"\n=== 验证完成 ===")
    
    # 总结
    print(f"\n📊 功能状态总结:")
    print(f"   数据迁移: {'✅' if migration_ok else '❌'}")
    print(f"   回复功能: {'✅' if total_replies > 0 else '⚠️  (无测试数据)'}")
    print(f"   线程功能: {'✅' if messages_with_replies > 0 else '⚠️  (无测试数据)'}")
    print(f"   字段完整: {'✅' if field_check else '❌'}")
    print(f"   关系一致: {'✅' if reply_consistency else '❌'}")
    
    if migration_ok and field_check and reply_consistency:
        print(f"\n🎉 所有核心功能验证通过！站内信回复功能已成功实现。")
        print(f"\n📝 使用说明:")
        print(f"   1. 访问 /messages 页面查看站内信")
        print(f"   2. 点击消息的'回复'按钮进行回复")
        print(f"   3. 点击'查看'按钮查看完整对话线程")
        print(f"   4. 管理员可在申请管理中使用此功能与用户交流")
    else:
        print(f"\n⚠️  发现问题，请检查上述错误信息")

if __name__ == "__main__":
    final_verification()
