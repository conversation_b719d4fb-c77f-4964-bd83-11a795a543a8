#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from message_system import MessageSystem

# 创建消息系统实例
ms = MessageSystem()

# 找到一个管理员发给用户的消息
target_message = None
for msg in ms.messages_data['messages']:
    if msg['sender'] == 'admin' and msg['recipient'] not in ['all', 'admin']:
        target_message = msg
        break

if target_message:
    print(f"Found target message: {target_message['title']}")
    print(f"Recipient: {target_message['recipient']}")
    
    # 创建用户回复
    success, reply_id = ms.send_message(
        sender=target_message['recipient'],
        recipient='admin',
        title=f"Re: {target_message['title']}",
        content="谢谢管理员！我想了解更多详情。",
        message_type='user',
        reply_to=target_message['id']
    )
    
    if success:
        print(f"Reply created successfully: {reply_id}")
        
        # 创建管理员回复
        success2, reply2_id = ms.send_message(
            sender='admin',
            recipient=target_message['recipient'],
            title=f"Re: {target_message['title']}",
            content="好的，我来为您详细解释一下相关情况...",
            message_type='admin',
            reply_to=target_message['id']
        )
        
        if success2:
            print(f"Admin reply created successfully: {reply2_id}")
            
            # 检查原消息的回复列表
            updated_msg = ms.get_message_by_id(target_message['id'])
            print(f"Original message now has {len(updated_msg['replies'])} replies")
            
        else:
            print(f"Failed to create admin reply: {reply2_id}")
    else:
        print(f"Failed to create reply: {reply_id}")
else:
    print("No suitable target message found")
